{"ast": null, "code": "var _jsxFileName = \"E:\\\\WDP301_UROOM\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\BookingCheckPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport axios from \"axios\";\nimport { Container, Row, Col, Card, Form, Button, InputGroup } from \"react-bootstrap\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\nimport Banner from \"../../../images/banner.jpg\";\nimport Header from \"../Header\";\nimport Footer from \"../Footer\";\nimport * as Routers from \"../../../utils/Routes\";\nimport { useNavigate } from \"react-router-dom\";\nimport ConfirmationModal from \"@components/ConfirmationModal\";\nimport PromotionModal from \"./components/PromotionModal\";\nimport PromotionErrorModal from \"./components/PromotionErrorModal\";\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\nimport Utils from \"../../../utils/Utils\";\nimport Factories from \"../../../redux/search/factories\";\nimport { ChatBox } from \"./HomePage\";\nimport SearchActions from \"../../../redux/search/actions\";\nimport HotelActions from \"@redux/hotel/actions\";\nimport HotelClosedModal from \"./components/HotelClosedModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingCheckPage = () => {\n  _s();\n  var _hotelDetail$hotelNam, _hotelDetail$address;\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\n  const Auth = useAppSelector(state => state.Auth.Auth);\n  const SearchInformation = useAppSelector(state => state.Search.SearchInformation);\n  const selectedRoomsTemps = useAppSelector(state => state.Search.selectedRooms);\n  const selectedServicesFromRedux = useAppSelector(state => state.Search.selectedServices);\n  const hotelDetailFromRedux = useAppSelector(state => state.Search.hotelDetail);\n  const navigate = useNavigate();\n  const dispatch = useAppDispatch();\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\n\n  // Promotion code state\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\n  const [promotionId, setPromotionId] = useState(null);\n\n  // Add state for booking data\n  const [bookingData, setBookingData] = useState({\n    selectedRooms: selectedRoomsTemps || [],\n    selectedServices: selectedServicesFromRedux || [],\n    hotelDetail: hotelDetailFromRedux || null,\n    searchInfo: SearchInformation\n  });\n  const [dataRestored, setDataRestored] = useState(false);\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\n  const [isValidatingPromotionBeforeBooking, setIsValidatingPromotionBeforeBooking] = useState(false);\n  const [isInitialLoading, setIsInitialLoading] = useState(true);\n\n  // Restore data from sessionStorage stack when component mounts\n  useEffect(() => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      const currentBooking = bookingStack[bookingStack.length - 1];\n      setBookingData(currentBooking);\n\n      // Update Redux store with current data\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: currentBooking.selectedRooms,\n          selectedServices: currentBooking.selectedServices,\n          hotelDetail: currentBooking.hotelDetail\n        }\n      });\n    }\n    setDataRestored(true);\n    setIsInitialLoading(false);\n  }, [dispatch]);\n\n  // Load promotion info from sessionStorage AFTER booking data is restored\n  useEffect(() => {\n    if (dataRestored) {\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\n      if (promo) {\n        // Check if promotion was saved more than 5 minutes ago\n        const savedTime = promo.savedTime || Date.now();\n        const timeDiff = Date.now() - savedTime;\n        const fiveMinutes = 5 * 60 * 1000;\n        if (timeDiff > fiveMinutes) {\n          // Auto-validate if promotion is old\n          console.log(\"Promotion is old, auto-validating...\");\n          setPromotionCode(promo.promotionCode || \"\");\n          setPromotionDiscount(promo.promotionDiscount || 0);\n          setPromotionMessage(\"Validating promotion...\");\n          setPromotionId(promo.promotionId || null);\n        } else {\n          setPromotionCode(promo.promotionCode || \"\");\n          setPromotionDiscount(promo.promotionDiscount || 0);\n          setPromotionMessage(promo.promotionMessage || \"\");\n          setPromotionId(promo.promotionId || null);\n        }\n      }\n    }\n  }, [dataRestored]);\n\n  // Save promotion info to sessionStorage when any promotion state changes\n  useEffect(() => {\n    if (dataRestored) {\n      // Chỉ save khi đã restore xong data\n      sessionStorage.setItem(\"promotionInfo\", JSON.stringify({\n        promotionCode,\n        promotionDiscount,\n        promotionMessage,\n        promotionId,\n        savedTime: Date.now() // Add timestamp for validation\n      }));\n    }\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\n\n  // Use bookingData instead of Redux state\n  const selectedRooms = bookingData.selectedRooms;\n  const selectedServices = bookingData.selectedServices;\n  const hotelDetail = bookingData.hotelDetail;\n  const searchInfo = bookingData.searchInfo;\n\n  // Calculate number of days between check-in and check-out\n  const calculateNumberOfDays = () => {\n    const checkIn = new Date(searchInfo.checkinDate);\n    const checkOut = new Date(searchInfo.checkoutDate);\n    const diffTime = Math.abs(checkOut - checkIn);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  const numberOfDays = calculateNumberOfDays();\n\n  // Calculate prices\n  const totalRoomPrice = selectedRooms.reduce((total, {\n    room,\n    amount\n  }) => total + room.price * amount * numberOfDays, 0);\n  const totalServicePrice = selectedServices.reduce((total, service) => {\n    const selectedDates = service.selectedDates || [];\n    const serviceQuantity = service.quantity * selectedDates.length;\n    return total + service.price * serviceQuantity;\n  }, 0);\n  const subtotal = totalRoomPrice + totalServicePrice;\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\n\n  // Validate promotion when data is restored or booking changes\n  useEffect(() => {\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\n\n    // Add a small delay to ensure promotion is fully restored before validation\n    const timeoutId = setTimeout(() => {\n      const validatePromotion = async () => {\n        setIsValidatingPromotion(true);\n        try {\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\n            code: promotionCode,\n            orderAmount: subtotal\n          });\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\n            // Promotion is no longer valid or discount changed\n            setPromotionCode(\"\");\n            setPromotionDiscount(0);\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\n            setPromotionId(null);\n            sessionStorage.removeItem(\"promotionInfo\");\n          }\n        } catch (err) {\n          // Promotion validation failed\n          setPromotionCode(\"\");\n          setPromotionDiscount(0);\n          setPromotionMessage(\"Promotion is no longer valid\");\n          setPromotionId(null);\n          sessionStorage.removeItem(\"promotionInfo\");\n        } finally {\n          setIsValidatingPromotion(false);\n        }\n      };\n      validatePromotion();\n    }, 100);\n    return () => clearTimeout(timeoutId);\n  }, [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount]); // Validate when subtotal changes or data is restored\n\n  // Handle navigation back to HomeDetailPage\n  const handleBackToHomeDetail = () => {\n    const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n    if (bookingStack.length > 0) {\n      // Remove the current booking from stack\n      bookingStack.pop();\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n    }\n    navigate(-1);\n  };\n\n  // Star rating component\n  const StarRating = ({\n    rating\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"star-rating\",\n      children: [...Array(5)].map((_, index) => index < rating ? /*#__PURE__*/_jsxDEV(FaStar, {\n        className: \"star filled\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(FaRegStar, {\n        className: \"star\"\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this);\n  };\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\n  const [showPromotionErrorModal, setShowPromotionErrorModal] = useState(false);\n  const [promotionErrorMessage, setPromotionErrorMessage] = useState(\"\");\n  const [invalidPromotionCode, setInvalidPromotionCode] = useState(\"\");\n\n  // Hàm xử lý áp dụng promotion từ modal\n  const handleApplyPromotionFromModal = promotionData => {\n    setPromotionCode(promotionData.code);\n    setPromotionDiscount(promotionData.discount);\n    setPromotionMessage(promotionData.message);\n    setPromotionId(promotionData.promotionId);\n  };\n\n  // Function to validate promotion before booking\n  const validatePromotionBeforeBooking = async () => {\n    if (!promotionCode || !promotionId || promotionDiscount === 0) {\n      return {\n        valid: true\n      }; // No promotion to validate\n    }\n    setIsValidatingPromotionBeforeBooking(true);\n    try {\n      const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\n        code: promotionCode,\n        orderAmount: subtotal\n      });\n      setIsValidatingPromotionBeforeBooking(false);\n      if (!res.data.valid) {\n        return {\n          valid: false,\n          message: res.data.message || \"Promotion is no longer valid\"\n        };\n      }\n      if (res.data.discount !== promotionDiscount) {\n        return {\n          valid: false,\n          message: \"Promotion discount has changed. Please reapply the promotion.\"\n        };\n      }\n      return {\n        valid: true\n      };\n    } catch (err) {\n      setIsValidatingPromotionBeforeBooking(false);\n      return {\n        valid: false,\n        message: \"Unable to validate promotion. Please try again.\"\n      };\n    }\n  };\n\n  // Function to check hotel status before booking\n  const checkHotelStatusBeforeBooking = async () => {\n    return new Promise((resolve, reject) => {\n      setIsCheckingHotelStatus(true);\n      dispatch({\n        type: HotelActions.FETCH_DETAIL_HOTEL,\n        payload: {\n          hotelId: hotelDetail._id,\n          userId: Auth._id,\n          onSuccess: hotel => {\n            setIsCheckingHotelStatus(false);\n            if (hotel.ownerStatus === \"ACTIVE\") {\n              resolve(hotel);\n            } else {\n              reject(new Error(\"Hotel is currently inactive\"));\n            }\n          },\n          onFailed: error => {\n            setIsCheckingHotelStatus(false);\n            reject(new Error(error || \"Failed to check hotel status\"));\n          },\n          onError: error => {\n            setIsCheckingHotelStatus(false);\n            reject(new Error(\"Server error while checking hotel status\"));\n          }\n        }\n      });\n    });\n  };\n  const createBooking = async () => {\n    try {\n      // Validate promotion first if there's one applied\n      const promotionValidation = await validatePromotionBeforeBooking();\n      if (!promotionValidation.valid) {\n        // Store error info for modal\n        setPromotionErrorMessage(promotionValidation.message);\n        setInvalidPromotionCode(promotionCode);\n\n        // Clear invalid promotion\n        setPromotionCode(\"\");\n        setPromotionDiscount(0);\n        setPromotionMessage(\"\");\n        setPromotionId(null);\n        sessionStorage.removeItem(\"promotionInfo\");\n\n        // Show error modal\n        setShowPromotionErrorModal(true);\n        return;\n      }\n\n      // Check hotel status\n      const hotel = await checkHotelStatusBeforeBooking();\n      console.log(\"Hotel detail fetched successfully:\", hotel);\n      const totalRoomPrice = selectedRooms.reduce((total, {\n        room,\n        amount\n      }) => total + room.price * amount * numberOfDays, 0);\n      const totalServicePrice = selectedServices.reduce((total, service) => {\n        const selectedDates = service.selectedDates || [];\n        const serviceQuantity = service.quantity * selectedDates.length;\n        return total + service.price * serviceQuantity;\n      }, 0);\n      const bookingSubtotal = totalRoomPrice + totalServicePrice;\n      const params = {\n        hotelId: hotelDetail._id,\n        checkOutDate: searchInfo.checkoutDate,\n        checkInDate: searchInfo.checkinDate,\n        totalPrice: bookingSubtotal,\n        // giá gốc\n        finalPrice: finalPrice,\n        // giá sau giảm giá\n        roomDetails: selectedRooms.map(({\n          room,\n          amount\n        }) => ({\n          room: {\n            _id: room._id\n          },\n          amount: amount\n        })),\n        serviceDetails: selectedServices.map(service => {\n          var _service$selectedDate;\n          return {\n            _id: service._id,\n            quantity: service.quantity * (((_service$selectedDate = service.selectedDates) === null || _service$selectedDate === void 0 ? void 0 : _service$selectedDate.length) || 0),\n            selectDate: service.selectedDates || []\n          };\n        }),\n        // Thêm promotionId và promotionDiscount nếu có\n        ...(promotionId && {\n          promotionId\n        }),\n        ...(promotionDiscount > 0 && {\n          promotionDiscount\n        })\n      };\n      console.log(\"params >> \", params);\n\n      // Helper function to save reservationId to bookingStack\n      const saveReservationIdToBookingStack = reservationId => {\n        if (reservationId) {\n          const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n          if (bookingStack.length > 0) {\n            bookingStack[bookingStack.length - 1].reservationId = reservationId;\n            sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\n          }\n        }\n      };\n      try {\n        let reservationId = null;\n        const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\n        if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\n          reservationId = bookingStack[bookingStack.length - 1].reservationId;\n        }\n        const response = await Factories.create_booking({\n          ...params,\n          reservationId\n        });\n        console.log(\"response >> \", response);\n        if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n          var _response$data, _response$data$unpaid, _responseCheckout$dat;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : (_response$data$unpaid = _response$data.unpaidReservation) === null || _response$data$unpaid === void 0 ? void 0 : _response$data$unpaid._id;\n          saveReservationIdToBookingStack(reservationId);\n          const unpaidReservationId = reservationId;\n          const responseCheckout = await Factories.checkout_booking(unpaidReservationId);\n          console.log(\"responseCheckout >> \", responseCheckout);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat = responseCheckout.data) === null || _responseCheckout$dat === void 0 ? void 0 : _responseCheckout$dat.sessionUrl;\n          if (paymentUrl) {\n            window.location.href = paymentUrl;\n          }\n        } else if ((response === null || response === void 0 ? void 0 : response.status) === 201) {\n          var _response$data2, _response$data2$reser, _responseCheckout$dat2;\n          reservationId = response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$reser = _response$data2.reservation) === null || _response$data2$reser === void 0 ? void 0 : _response$data2$reser._id;\n          saveReservationIdToBookingStack(reservationId);\n          const responseCheckout = await Factories.checkout_booking(reservationId);\n          const paymentUrl = responseCheckout === null || responseCheckout === void 0 ? void 0 : (_responseCheckout$dat2 = responseCheckout.data) === null || _responseCheckout$dat2 === void 0 ? void 0 : _responseCheckout$dat2.sessionUrl;\n          if (paymentUrl) {\n            window.location.href = paymentUrl;\n          }\n        } else {\n          console.log(\"error create booking\");\n        }\n      } catch (error) {\n        console.error(\"Error create payment: \", error);\n        navigate(Routers.ErrorPage);\n      }\n    } catch (error) {\n      console.error(\"Error checking hotel status:\", error);\n      setShowModalStatusBooking(true);\n    }\n  };\n  const handleAccept = async () => {\n    const totalRoomPrice = selectedRooms.reduce((total, {\n      room,\n      amount\n    }) => total + room.price * amount * numberOfDays, 0);\n    if (totalRoomPrice > 0) {\n      // Final validation before creating booking\n      await createBooking();\n\n      // Only clear selection if booking was successful\n      // (createBooking will handle errors and not reach this point if failed)\n      dispatch({\n        type: SearchActions.SAVE_SELECTED_ROOMS,\n        payload: {\n          selectedRooms: [],\n          selectedServices: [],\n          hotelDetail: hotelDetail\n        }\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    setShowAcceptModal(true);\n  };\n  const formatCurrency = amount => {\n    if (amount === undefined || amount === null) return \"$0\";\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"USD\",\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(amount);\n  };\n\n  // Only show loading spinner during initial load, not during re-renders\n  if (isInitialLoading || !hotelDetail && !dataRestored) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"100vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If data is restored but hotelDetail is still missing, redirect back\n  if (!hotelDetail && dataRestored) {\n    navigate(-1);\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"65px\",\n        paddingBottom: \"50px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Container, {\n        className: \"mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"booking-card text-white\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                marginBottom: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stars mb-2\",\n                style: {\n                  justifyContent: \"flex-start\",\n                  justifyItems: \"self-start\"\n                },\n                children: /*#__PURE__*/_jsxDEV(StarRating, {\n                  rating: hotelDetail.star\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"hotel-name mb-1\",\n                children: (_hotelDetail$hotelNam = hotelDetail.hotelName) !== null && _hotelDetail$hotelNam !== void 0 ? _hotelDetail$hotelNam : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hotel-address small mb-4\",\n                children: (_hotelDetail$address = hotelDetail.address) !== null && _hotelDetail$address !== void 0 ? _hotelDetail$address : \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-4\",\n                children: \"Your booking detail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkin\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkinDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"checkout\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"small mb-1 fw-bold\",\n                      style: {\n                        fontSize: 20\n                      },\n                      children: \"Checkout\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"time\",\n                      children: Utils.getDate(searchInfo.checkoutDate, 1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stay-info mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total length of stay:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [numberOfDays, \" night\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this), \" \"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total number of people:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: [searchInfo.adults, \" Adults - \", searchInfo.childrens, \" \", \"Childrens\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-room mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-4\",\n                  children: \"You selected\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 19\n                }, this), selectedRooms.map(({\n                  room,\n                  amount\n                }) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [amount, \" x \", room.name, \" (\", numberOfDays, \" days):\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(room.price * amount * numberOfDays)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 23\n                  }, this)]\n                }, room._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 21\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: handleBackToHomeDetail,\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 17\n              }, this), selectedServices.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-services mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-3\",\n                  children: \"Selected Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 21\n                }, this), selectedServices.map(service => {\n                  const selectedDates = service.selectedDates || [];\n                  const serviceQuantity = service.quantity * selectedDates.length;\n                  const serviceTotal = service.price * serviceQuantity;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [service.quantity, \" x \", service.name, \" (\", selectedDates.length, \" days):\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 647,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"fw-bold\",\n                      children: Utils.formatCurrency(serviceTotal)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 651,\n                      columnNumber: 27\n                    }, this)]\n                  }, service._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 25\n                  }, this);\n                }), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    className: \"text-blue text-decoration-none\",\n                    style: {\n                      cursor: \"pointer\"\n                    },\n                    onClick: () => {\n                      dispatch({\n                        type: SearchActions.SAVE_SELECTED_ROOMS,\n                        payload: {\n                          selectedRooms: selectedRooms,\n                          selectedServices: selectedServices,\n                          hotelDetail: hotelDetail\n                        }\n                      });\n                      navigate(-1);\n                    },\n                    children: \"Change your selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 659,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"booking-divider mb-3\",\n                style: {\n                  height: \"1px\",\n                  backgroundColor: \"rgba(255,255,255,0.2)\",\n                  margin: \"15px 0\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"promotion-section mb-3\",\n                children: [promotionDiscount > 0 ? /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-applied mb-3\",\n                  style: {\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n                    borderColor: \"#28a745\",\n                    border: \"2px solid #28a745\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"text-success me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 704,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"fw-bold text-success\",\n                            children: promotionCode\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 705,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 703,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-success\",\n                          children: [\"Save \", Utils.formatCurrency(promotionDiscount)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 707,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 702,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"outline-danger\",\n                        size: \"sm\",\n                        onClick: () => handleApplyPromotionFromModal({\n                          code: \"\",\n                          discount: 0,\n                          message: \"\",\n                          promotionId: null\n                        }),\n                        className: \"d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                          className: \"me-1\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 722,\n                          columnNumber: 29\n                        }, this), \"Remove\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-3 mb-3\",\n                  style: {\n                    border: \"2px dashed rgba(255,255,255,0.3)\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"rgba(255,255,255,0.05)\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"text-muted mb-2\",\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-muted small\",\n                    children: \"No promotion applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"w-100 d-flex align-items-center justify-content-center\",\n                  onClick: () => setShowPromotionModal(true),\n                  style: {\n                    borderStyle: \"dashed\",\n                    borderWidth: \"2px\",\n                    padding: \"12px\"\n                  },\n                  disabled: isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                  children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 21\n                  }, this), isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"Validating...\" : promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 19\n                }, this), (isValidatingPromotion || isValidatingPromotionBeforeBooking) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"spinner-border spinner-border-sm me-1\",\n                      role: \"status\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"visually-hidden\",\n                        children: \"Loading...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 760,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 759,\n                      columnNumber: 25\n                    }, this), \"Checking promotion validity...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-breakdown\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Subtotal:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 771,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold\",\n                    children: Utils.formatCurrency(subtotal)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 19\n                }, this), promotionDiscount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success\",\n                    children: \"Discount:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-bold text-success\",\n                    children: [\"-\", Utils.formatCurrency(promotionDiscount)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"booking-divider mb-2\",\n                  style: {\n                    height: \"1px\",\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                    margin: \"10px 0\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger mb-0\",\n                    children: [\"Total: \", Utils.formatCurrency(finalPrice)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: \"Includes taxes and fees\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 5,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"info-card\",\n              style: {\n                backgroundColor: \"rgba(20, 30, 70, 0.85)\",\n                borderRadius: \"10px\",\n                padding: \"20px\",\n                color: \"white\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-4\",\n                children: \"Check your information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Full name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: Auth.name,\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"email\",\n                    value: Auth.email,\n                    placeholder: \"<EMAIL>\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 843,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"tel\",\n                    value: Auth.phoneNumber,\n                    placeholder: \"0912345678\",\n                    className: \"bg-transparent text-white\",\n                    style: {\n                      border: \"1px solid rgba(255,255,255,0.3)\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Who are you booking for?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"mainGuest\",\n                      label: \"I'm the main guest\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"mainGuest\",\n                      onChange: () => setBookingFor(\"mainGuest\"),\n                      className: \"mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 859,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n                      type: \"radio\",\n                      id: \"someoneElse\",\n                      label: \"I'm booking for someone else\",\n                      name: \"bookingFor\",\n                      checked: bookingFor === \"someoneElse\",\n                      onChange: () => setBookingFor(\"someoneElse\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    className: \"px-4 py-2\",\n                    style: {\n                      borderRadius: \"10px\",\n                      backgroundColor: \"white\",\n                      color: \"#007bff\",\n                      border: \"none\",\n                      fontWeight: \"bold\"\n                    },\n                    onClick: handleConfirmBooking,\n                    disabled: isCheckingHotelStatus || isValidatingPromotion || isValidatingPromotionBeforeBooking,\n                    children: isValidatingPromotionBeforeBooking ? \"Validating Promotion...\" : isCheckingHotelStatus ? \"Checking Hotel...\" : \"Booking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 880,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n                    show: showAcceptModal,\n                    onHide: () => setShowAcceptModal(false),\n                    onConfirm: handleAccept,\n                    title: \"Confirm Acceptance\",\n                    message: \"Do you want to proceed with this booking confirmation?\",\n                    confirmButtonText: \"Accept\",\n                    type: \"accept\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 896,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ChatBox, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 912,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 911,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 915,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionModal, {\n      show: showPromotionModal,\n      onHide: () => setShowPromotionModal(false),\n      totalPrice: subtotal,\n      onApplyPromotion: handleApplyPromotionFromModal,\n      currentPromotionId: promotionId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 918,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HotelClosedModal, {\n      show: showModalStatusBooking,\n      onClose: () => {\n        setShowModalStatusBooking(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 926,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PromotionErrorModal, {\n      show: showPromotionErrorModal,\n      onClose: () => {\n        setShowPromotionErrorModal(false);\n        setPromotionErrorMessage(\"\");\n        setInvalidPromotionCode(\"\");\n      },\n      onSelectNewPromotion: () => {\n        setShowPromotionErrorModal(false);\n        setPromotionErrorMessage(\"\");\n        setInvalidPromotionCode(\"\");\n        setShowPromotionModal(true);\n      },\n      errorMessage: promotionErrorMessage,\n      promotionCode: invalidPromotionCode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 934,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 494,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingCheckPage, \"5EEva92OytmZBYYvTP8mGb3RTmA=\", false, function () {\n  return [useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useNavigate, useAppDispatch];\n});\n_c = BookingCheckPage;\nexport default BookingCheckPage;\nvar _c;\n$RefreshReg$(_c, \"BookingCheckPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "InputGroup", "FaStar", "FaRegStar", "FaTag", "FaTimes", "Banner", "Header", "Footer", "Routers", "useNavigate", "ConfirmationModal", "PromotionModal", "PromotionErrorModal", "useAppSelector", "useAppDispatch", "Utils", "Factories", "ChatBox", "SearchActions", "HotelActions", "HotelClosedModal", "jsxDEV", "_jsxDEV", "BookingCheckPage", "_s", "_hotelDetail$hotelNam", "_hotelDetail$address", "showModalStatusBooking", "setShowModalStatusBooking", "<PERSON><PERSON>", "state", "SearchInformation", "Search", "selectedRoomsTemps", "selectedRooms", "selectedServicesFromRedux", "selectedServices", "hotelDetailFromRedux", "hotelDetail", "navigate", "dispatch", "bookingFor", "setBookingFor", "promotionCode", "setPromotionCode", "promotionDiscount", "setPromotionDiscount", "promotionMessage", "setPromotionMessage", "promotionId", "setPromotionId", "bookingData", "setBookingData", "searchInfo", "dataRestored", "setDataRestored", "isValidatingPromotion", "setIsValidatingPromotion", "isCheckingHotelStatus", "setIsCheckingHotelStatus", "isValidatingPromotionBeforeBooking", "setIsValidatingPromotionBeforeBooking", "isInitialLoading", "setIsInitialLoading", "bookingStack", "JSON", "parse", "sessionStorage", "getItem", "length", "currentBooking", "type", "SAVE_SELECTED_ROOMS", "payload", "promo", "savedTime", "Date", "now", "timeDiff", "fiveMinutes", "console", "log", "setItem", "stringify", "calculateNumberOfDays", "checkIn", "checkinDate", "checkOut", "checkoutDate", "diffTime", "Math", "abs", "diffDays", "ceil", "numberOfDays", "totalRoomPrice", "reduce", "total", "room", "amount", "price", "totalServicePrice", "service", "selectedDates", "serviceQuantity", "quantity", "subtotal", "finalPrice", "max", "timeoutId", "setTimeout", "validatePromotion", "res", "post", "code", "orderAmount", "data", "valid", "discount", "removeItem", "err", "clearTimeout", "handleBackToHomeDetail", "pop", "StarRating", "rating", "className", "children", "Array", "map", "_", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showAcceptModal", "setShowAcceptModal", "showPromotionModal", "setShowPromotionModal", "showPromotionErrorModal", "setShowPromotionErrorModal", "promotionErrorMessage", "setPromotionErrorMessage", "invalidPromotionCode", "setInvalidPromotionCode", "handleApplyPromotionFromModal", "promotionData", "message", "validatePromotionBeforeBooking", "checkHotelStatusBeforeBooking", "Promise", "resolve", "reject", "FETCH_DETAIL_HOTEL", "hotelId", "_id", "userId", "onSuccess", "hotel", "ownerStatus", "Error", "onFailed", "error", "onError", "createBooking", "promotionValidation", "bookingSubtotal", "params", "checkOutDate", "checkInDate", "totalPrice", "roomDetails", "serviceDetails", "_service$selectedDate", "selectDate", "saveReservationIdToBookingStack", "reservationId", "response", "create_booking", "status", "_response$data", "_response$data$unpaid", "_responseCheckout$dat", "unpaidReservation", "unpaidReservationId", "responseCheckout", "checkout_booking", "paymentUrl", "sessionUrl", "window", "location", "href", "_response$data2", "_response$data2$reser", "_responseCheckout$dat2", "reservation", "ErrorPage", "handleAccept", "handleConfirmBooking", "formatCurrency", "undefined", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "height", "role", "backgroundImage", "backgroundSize", "backgroundPosition", "paddingTop", "paddingBottom", "md", "lg", "backgroundColor", "borderRadius", "padding", "marginBottom", "justifyContent", "justifyItems", "star", "hotelName", "address", "margin", "xs", "fontSize", "getDate", "adults", "childrens", "name", "cursor", "onClick", "serviceTotal", "borderColor", "border", "Body", "variant", "size", "borderStyle", "borderWidth", "disabled", "color", "Group", "Label", "Control", "value", "email", "placeholder", "phoneNumber", "Check", "id", "label", "checked", "onChange", "fontWeight", "show", "onHide", "onConfirm", "title", "confirmButtonText", "onApplyPromotion", "currentPromotionId", "onClose", "onSelectNewPromotion", "errorMessage", "_c", "$RefreshReg$"], "sources": ["E:/WDP301_UROOM/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Container,\r\n  Row,\r\n  Col,\r\n  Card,\r\n  Form,\r\n  Button,\r\n  InputGroup,\r\n} from \"react-bootstrap\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaStar, FaRegStar, FaTag, FaTimes } from \"react-icons/fa\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport Header from \"../Header\";\r\nimport Footer from \"../Footer\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport ConfirmationModal from \"@components/ConfirmationModal\";\r\nimport PromotionModal from \"./components/PromotionModal\";\r\nimport PromotionErrorModal from \"./components/PromotionErrorModal\";\r\nimport { useAppSelector, useAppDispatch } from \"../../../redux/store\";\r\nimport Utils from \"../../../utils/Utils\";\r\nimport Factories from \"../../../redux/search/factories\";\r\nimport { ChatBox } from \"./HomePage\";\r\nimport SearchActions from \"../../../redux/search/actions\";\r\nimport HotelActions from \"@redux/hotel/actions\";\r\nimport HotelClosedModal from \"./components/HotelClosedModal\";\r\n\r\nconst BookingCheckPage = () => {\r\n  const [showModalStatusBooking, setShowModalStatusBooking] = useState(false);\r\n\r\n  const Auth = useAppSelector((state) => state.Auth.Auth);\r\n  const SearchInformation = useAppSelector(\r\n    (state) => state.Search.SearchInformation\r\n  );\r\n  const selectedRoomsTemps = useAppSelector(\r\n    (state) => state.Search.selectedRooms\r\n  );\r\n  const selectedServicesFromRedux = useAppSelector(\r\n    (state) => state.Search.selectedServices\r\n  );\r\n  const hotelDetailFromRedux = useAppSelector(\r\n    (state) => state.Search.hotelDetail\r\n  );\r\n  const navigate = useNavigate();\r\n  const dispatch = useAppDispatch();\r\n  const [bookingFor, setBookingFor] = useState(\"mainGuest\");\r\n\r\n  // Promotion code state\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [promotionDiscount, setPromotionDiscount] = useState(0);\r\n  const [promotionMessage, setPromotionMessage] = useState(\"\");\r\n  const [promotionId, setPromotionId] = useState(null);\r\n\r\n  // Add state for booking data\r\n  const [bookingData, setBookingData] = useState({\r\n    selectedRooms: selectedRoomsTemps || [],\r\n    selectedServices: selectedServicesFromRedux || [],\r\n    hotelDetail: hotelDetailFromRedux || null,\r\n    searchInfo: SearchInformation,\r\n  });\r\n\r\n  const [dataRestored, setDataRestored] = useState(false);\r\n  const [isValidatingPromotion, setIsValidatingPromotion] = useState(false);\r\n  const [isCheckingHotelStatus, setIsCheckingHotelStatus] = useState(false);\r\n  const [isValidatingPromotionBeforeBooking, setIsValidatingPromotionBeforeBooking] = useState(false);\r\n  const [isInitialLoading, setIsInitialLoading] = useState(true);\r\n\r\n  // Restore data from sessionStorage stack when component mounts\r\n  useEffect(() => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      const currentBooking = bookingStack[bookingStack.length - 1];\r\n      setBookingData(currentBooking);\r\n\r\n      // Update Redux store with current data\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: currentBooking.selectedRooms,\r\n          selectedServices: currentBooking.selectedServices,\r\n          hotelDetail: currentBooking.hotelDetail,\r\n        },\r\n      });\r\n    }\r\n    setDataRestored(true);\r\n    setIsInitialLoading(false);\r\n  }, [dispatch]);\r\n\r\n  // Load promotion info from sessionStorage AFTER booking data is restored\r\n  useEffect(() => {\r\n    if (dataRestored) {\r\n      const promo = JSON.parse(sessionStorage.getItem(\"promotionInfo\") || \"null\");\r\n      if (promo) {\r\n        // Check if promotion was saved more than 5 minutes ago\r\n        const savedTime = promo.savedTime || Date.now();\r\n        const timeDiff = Date.now() - savedTime;\r\n        const fiveMinutes = 5 * 60 * 1000;\r\n\r\n        if (timeDiff > fiveMinutes) {\r\n          // Auto-validate if promotion is old\r\n          console.log(\"Promotion is old, auto-validating...\");\r\n          setPromotionCode(promo.promotionCode || \"\");\r\n          setPromotionDiscount(promo.promotionDiscount || 0);\r\n          setPromotionMessage(\"Validating promotion...\");\r\n          setPromotionId(promo.promotionId || null);\r\n        } else {\r\n          setPromotionCode(promo.promotionCode || \"\");\r\n          setPromotionDiscount(promo.promotionDiscount || 0);\r\n          setPromotionMessage(promo.promotionMessage || \"\");\r\n          setPromotionId(promo.promotionId || null);\r\n        }\r\n      }\r\n    }\r\n  }, [dataRestored]);\r\n\r\n  // Save promotion info to sessionStorage when any promotion state changes\r\n  useEffect(() => {\r\n    if (dataRestored) { // Chỉ save khi đã restore xong data\r\n      sessionStorage.setItem(\r\n        \"promotionInfo\",\r\n        JSON.stringify({\r\n          promotionCode,\r\n          promotionDiscount,\r\n          promotionMessage,\r\n          promotionId,\r\n          savedTime: Date.now(), // Add timestamp for validation\r\n        })\r\n      );\r\n    }\r\n  }, [promotionCode, promotionDiscount, promotionMessage, promotionId, dataRestored]);\r\n\r\n  // Use bookingData instead of Redux state\r\n  const selectedRooms = bookingData.selectedRooms;\r\n  const selectedServices = bookingData.selectedServices;\r\n  const hotelDetail = bookingData.hotelDetail;\r\n  const searchInfo = bookingData.searchInfo;\r\n\r\n  // Calculate number of days between check-in and check-out\r\n  const calculateNumberOfDays = () => {\r\n    const checkIn = new Date(searchInfo.checkinDate);\r\n    const checkOut = new Date(searchInfo.checkoutDate);\r\n    const diffTime = Math.abs(checkOut - checkIn);\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  };\r\n\r\n  const numberOfDays = calculateNumberOfDays();\r\n\r\n  // Calculate prices\r\n  const totalRoomPrice = selectedRooms.reduce(\r\n    (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n    0\r\n  );\r\n  const totalServicePrice = selectedServices.reduce((total, service) => {\r\n    const selectedDates = service.selectedDates || [];\r\n    const serviceQuantity = service.quantity * selectedDates.length;\r\n    return total + service.price * serviceQuantity;\r\n  }, 0);\r\n  const subtotal = totalRoomPrice + totalServicePrice;\r\n  const finalPrice = Math.max(subtotal - promotionDiscount, 0);\r\n\r\n  // Validate promotion when data is restored or booking changes\r\n  useEffect(() => {\r\n    if (!dataRestored || !promotionCode || !promotionId || promotionDiscount === 0) return;\r\n\r\n    // Add a small delay to ensure promotion is fully restored before validation\r\n    const timeoutId = setTimeout(() => {\r\n      const validatePromotion = async () => {\r\n        setIsValidatingPromotion(true);\r\n        try {\r\n          const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\r\n            code: promotionCode,\r\n            orderAmount: subtotal,\r\n          });\r\n\r\n          if (!res.data.valid || res.data.discount !== promotionDiscount) {\r\n            // Promotion is no longer valid or discount changed\r\n            setPromotionCode(\"\");\r\n            setPromotionDiscount(0);\r\n            setPromotionMessage(\"Promotion is no longer valid due to booking changes\");\r\n            setPromotionId(null);\r\n            sessionStorage.removeItem(\"promotionInfo\");\r\n          }\r\n        } catch (err) {\r\n          // Promotion validation failed\r\n          setPromotionCode(\"\");\r\n          setPromotionDiscount(0);\r\n          setPromotionMessage(\"Promotion is no longer valid\");\r\n          setPromotionId(null);\r\n          sessionStorage.removeItem(\"promotionInfo\");\r\n        } finally {\r\n          setIsValidatingPromotion(false);\r\n        }\r\n      };\r\n\r\n      validatePromotion();\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [dataRestored, subtotal, promotionCode, promotionId, promotionDiscount]); // Validate when subtotal changes or data is restored\r\n\r\n  // Handle navigation back to HomeDetailPage\r\n  const handleBackToHomeDetail = () => {\r\n    const bookingStack = JSON.parse(\r\n      sessionStorage.getItem(\"bookingStack\") || \"[]\"\r\n    );\r\n    if (bookingStack.length > 0) {\r\n      // Remove the current booking from stack\r\n      bookingStack.pop();\r\n      sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n    }\r\n    navigate(-1);\r\n  };\r\n\r\n  // Star rating component\r\n  const StarRating = ({ rating }) => {\r\n    return (\r\n      <div className=\"star-rating\">\r\n        {[...Array(5)].map((_, index) =>\r\n          index < rating ? (\r\n            <FaStar key={index} className=\"star filled\" />\r\n          ) : (\r\n            <FaRegStar key={index} className=\"star\" />\r\n          )\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const [showAcceptModal, setShowAcceptModal] = useState(false);\r\n  const [showPromotionModal, setShowPromotionModal] = useState(false);\r\n  const [showPromotionErrorModal, setShowPromotionErrorModal] = useState(false);\r\n  const [promotionErrorMessage, setPromotionErrorMessage] = useState(\"\");\r\n  const [invalidPromotionCode, setInvalidPromotionCode] = useState(\"\");\r\n\r\n  // Hàm xử lý áp dụng promotion từ modal\r\n  const handleApplyPromotionFromModal = (promotionData) => {\r\n    setPromotionCode(promotionData.code);\r\n    setPromotionDiscount(promotionData.discount);\r\n    setPromotionMessage(promotionData.message);\r\n    setPromotionId(promotionData.promotionId);\r\n  };\r\n\r\n  // Function to validate promotion before booking\r\n  const validatePromotionBeforeBooking = async () => {\r\n    if (!promotionCode || !promotionId || promotionDiscount === 0) {\r\n      return { valid: true }; // No promotion to validate\r\n    }\r\n\r\n    setIsValidatingPromotionBeforeBooking(true);\r\n    try {\r\n      const res = await axios.post(\"http://localhost:5000/api/promotions/apply\", {\r\n        code: promotionCode,\r\n        orderAmount: subtotal,\r\n      });\r\n\r\n      setIsValidatingPromotionBeforeBooking(false);\r\n\r\n      if (!res.data.valid) {\r\n        return {\r\n          valid: false,\r\n          message: res.data.message || \"Promotion is no longer valid\"\r\n        };\r\n      }\r\n\r\n      if (res.data.discount !== promotionDiscount) {\r\n        return {\r\n          valid: false,\r\n          message: \"Promotion discount has changed. Please reapply the promotion.\"\r\n        };\r\n      }\r\n\r\n      return { valid: true };\r\n    } catch (err) {\r\n      setIsValidatingPromotionBeforeBooking(false);\r\n      return {\r\n        valid: false,\r\n        message: \"Unable to validate promotion. Please try again.\"\r\n      };\r\n    }\r\n  };\r\n\r\n  // Function to check hotel status before booking\r\n  const checkHotelStatusBeforeBooking = async () => {\r\n    return new Promise((resolve, reject) => {\r\n      setIsCheckingHotelStatus(true);\r\n      dispatch({\r\n        type: HotelActions.FETCH_DETAIL_HOTEL,\r\n        payload: {\r\n          hotelId: hotelDetail._id,\r\n          userId: Auth._id,\r\n          onSuccess: (hotel) => {\r\n            setIsCheckingHotelStatus(false);\r\n            if (hotel.ownerStatus === \"ACTIVE\") {\r\n              resolve(hotel);\r\n            } else {\r\n              reject(new Error(\"Hotel is currently inactive\"));\r\n            }\r\n          },\r\n          onFailed: (error) => {\r\n            setIsCheckingHotelStatus(false);\r\n            reject(new Error(error || \"Failed to check hotel status\"));\r\n          },\r\n          onError: (error) => {\r\n            setIsCheckingHotelStatus(false);\r\n            reject(new Error(\"Server error while checking hotel status\"));\r\n          }\r\n        },\r\n      });\r\n    });\r\n  };\r\n\r\n  const createBooking = async () => {\r\n    try {\r\n      // Validate promotion first if there's one applied\r\n      const promotionValidation = await validatePromotionBeforeBooking();\r\n      if (!promotionValidation.valid) {\r\n        // Store error info for modal\r\n        setPromotionErrorMessage(promotionValidation.message);\r\n        setInvalidPromotionCode(promotionCode);\r\n\r\n        // Clear invalid promotion\r\n        setPromotionCode(\"\");\r\n        setPromotionDiscount(0);\r\n        setPromotionMessage(\"\");\r\n        setPromotionId(null);\r\n        sessionStorage.removeItem(\"promotionInfo\");\r\n\r\n        // Show error modal\r\n        setShowPromotionErrorModal(true);\r\n        return;\r\n      }\r\n\r\n      // Check hotel status\r\n      const hotel = await checkHotelStatusBeforeBooking();\r\n      console.log(\"Hotel detail fetched successfully:\", hotel);\r\n            const totalRoomPrice = selectedRooms.reduce(\r\n              (total, { room, amount }) =>\r\n                total + room.price * amount * numberOfDays,\r\n              0\r\n            );\r\n\r\n            const totalServicePrice = selectedServices.reduce(\r\n              (total, service) => {\r\n                const selectedDates = service.selectedDates || [];\r\n                const serviceQuantity = service.quantity * selectedDates.length;\r\n                return total + service.price * serviceQuantity;\r\n              },\r\n              0\r\n            );\r\n\r\n            const bookingSubtotal = totalRoomPrice + totalServicePrice;\r\n\r\n            const params = {\r\n              hotelId: hotelDetail._id,\r\n              checkOutDate: searchInfo.checkoutDate,\r\n              checkInDate: searchInfo.checkinDate,\r\n              totalPrice: bookingSubtotal, // giá gốc\r\n              finalPrice: finalPrice, // giá sau giảm giá\r\n              roomDetails: selectedRooms.map(({ room, amount }) => ({\r\n                room: {\r\n                  _id: room._id,\r\n                },\r\n                amount: amount,\r\n              })),\r\n              serviceDetails: selectedServices.map((service) => ({\r\n                _id: service._id,\r\n                quantity:\r\n                  service.quantity * (service.selectedDates?.length || 0),\r\n                selectDate: service.selectedDates || [],\r\n              })),\r\n              // Thêm promotionId và promotionDiscount nếu có\r\n              ...(promotionId && { promotionId }),\r\n              ...(promotionDiscount > 0 && { promotionDiscount }),\r\n            };\r\n\r\n            console.log(\"params >> \", params);\r\n\r\n            // Helper function to save reservationId to bookingStack\r\n            const saveReservationIdToBookingStack = (reservationId) => {\r\n              if (reservationId) {\r\n                const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n                if (bookingStack.length > 0) {\r\n                  bookingStack[bookingStack.length - 1].reservationId = reservationId;\r\n                  sessionStorage.setItem(\"bookingStack\", JSON.stringify(bookingStack));\r\n                }\r\n              }\r\n            };\r\n            try {\r\n              let reservationId = null;\r\n              const bookingStack = JSON.parse(sessionStorage.getItem(\"bookingStack\") || \"[]\");\r\n              if (bookingStack.length > 0 && bookingStack[bookingStack.length - 1].reservationId) {\r\n                reservationId = bookingStack[bookingStack.length - 1].reservationId;\r\n              }\r\n              const response = await Factories.create_booking({ ...params, reservationId });\r\n              console.log(\"response >> \", response);\r\n              if (response?.status === 200) {\r\n                reservationId = response?.data?.unpaidReservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const unpaidReservationId = reservationId;\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  unpaidReservationId\r\n                );\r\n                console.log(\"responseCheckout >> \", responseCheckout);\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else if (response?.status === 201) {\r\n                reservationId = response?.data?.reservation?._id;\r\n                saveReservationIdToBookingStack(reservationId);\r\n                const responseCheckout = await Factories.checkout_booking(\r\n                  reservationId\r\n                );\r\n                const paymentUrl = responseCheckout?.data?.sessionUrl;\r\n                if (paymentUrl) {\r\n                  window.location.href = paymentUrl;\r\n                }\r\n              } else {\r\n                console.log(\"error create booking\");\r\n              }\r\n            } catch (error) {\r\n              console.error(\"Error create payment: \", error);\r\n              navigate(Routers.ErrorPage);\r\n            }\r\n    } catch (error) {\r\n      console.error(\"Error checking hotel status:\", error);\r\n      setShowModalStatusBooking(true);\r\n    }\r\n  };\r\n\r\n  const handleAccept = async () => {\r\n    const totalRoomPrice = selectedRooms.reduce(\r\n      (total, { room, amount }) => total + room.price * amount * numberOfDays,\r\n      0\r\n    );\r\n\r\n    if (totalRoomPrice > 0) {\r\n      // Final validation before creating booking\r\n      await createBooking();\r\n\r\n      // Only clear selection if booking was successful\r\n      // (createBooking will handle errors and not reach this point if failed)\r\n      dispatch({\r\n        type: SearchActions.SAVE_SELECTED_ROOMS,\r\n        payload: {\r\n          selectedRooms: [],\r\n          selectedServices: [],\r\n          hotelDetail: hotelDetail,\r\n        },\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleConfirmBooking = () => {\r\n    setShowAcceptModal(true);\r\n  };\r\n\r\n  const formatCurrency = (amount) => {\r\n    if (amount === undefined || amount === null) return \"$0\";\r\n    return new Intl.NumberFormat(\"en-US\", {\r\n      style: \"currency\",\r\n      currency: \"USD\",\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 0,\r\n    }).format(amount);\r\n  };\r\n\r\n  // Only show loading spinner during initial load, not during re-renders\r\n  if (isInitialLoading || (!hotelDetail && !dataRestored)) {\r\n    return (\r\n      <div\r\n        className=\"d-flex justify-content-center align-items-center\"\r\n        style={{ height: \"100vh\" }}\r\n      >\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // If data is restored but hotelDetail is still missing, redirect back\r\n  if (!hotelDetail && dataRestored) {\r\n    navigate(-1);\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"65px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Container className=\"mt-4\">\r\n          <Row className=\"justify-content-center\">\r\n            {/* Left Card - Booking Details */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"booking-card text-white\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  marginBottom: \"20px\",\r\n                }}\r\n              >\r\n                <div\r\n                  className=\"stars mb-2\"\r\n                  style={{\r\n                    justifyContent: \"flex-start\",\r\n                    justifyItems: \"self-start\",\r\n                  }}\r\n                >\r\n                  <StarRating rating={hotelDetail.star} />\r\n                </div>\r\n\r\n                <h4 className=\"hotel-name mb-1\">\r\n                  {hotelDetail.hotelName ?? \"\"}\r\n                </h4>\r\n\r\n                <p className=\"hotel-address small mb-4\">\r\n                  {hotelDetail.address ?? \"\"}\r\n                </p>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <h5 className=\"mb-4\">Your booking detail</h5>\r\n\r\n                <Row className=\"mb-4\">\r\n                  <Col xs={6}>\r\n                    <div className=\"checkin\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkin\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkinDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                  <Col xs={6}>\r\n                    <div className=\"checkout\">\r\n                      <div\r\n                        className=\"small mb-1 fw-bold\"\r\n                        style={{ fontSize: 20 }}\r\n                      >\r\n                        Checkout\r\n                      </div>\r\n                      <div className=\"time\">\r\n                        {Utils.getDate(searchInfo.checkoutDate, 1)}\r\n                      </div>\r\n                    </div>\r\n                  </Col>\r\n                </Row>\r\n\r\n                <div className=\"stay-info mb-2\">\r\n                  <div className=\"d-flex justify-content-between mb-2\">\r\n                    <span>Total length of stay:</span>\r\n                    <span className=\"fw-bold\">{numberOfDays} night</span>{\" \"}\r\n                  </div>\r\n                  <div className=\"d-flex justify-content-between mb-3\">\r\n                    <span>Total number of people:</span>\r\n                    <span className=\"fw-bold\">\r\n                      {searchInfo.adults} Adults - {searchInfo.childrens}{\" \"}\r\n                      Childrens\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"selected-room mb-2\">\r\n                  <h5 className=\"mb-4\">You selected</h5>\r\n\r\n                  {selectedRooms.map(({ room, amount }) => (\r\n                    <div\r\n                      key={room._id}\r\n                      className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                    >\r\n                      <span>\r\n                        {amount} x {room.name} ({numberOfDays} days):\r\n                      </span>\r\n                      <span className=\"fw-bold\">\r\n                        {Utils.formatCurrency(\r\n                          room.price * amount * numberOfDays\r\n                        )}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n\r\n                  <div className=\"small mb-3\">\r\n                    <a\r\n                      className=\"text-blue text-decoration-none\"\r\n                      style={{ cursor: \"pointer\" }}\r\n                      onClick={handleBackToHomeDetail}\r\n                    >\r\n                      Change your selection\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Selected Services Section */}\r\n                {selectedServices.length > 0 && (\r\n                  <div className=\"selected-services mb-2\">\r\n                    <h5 className=\"mb-3\">Selected Services</h5>\r\n\r\n                    {selectedServices.map((service) => {\r\n                      const selectedDates = service.selectedDates || [];\r\n                      const serviceQuantity =\r\n                        service.quantity * selectedDates.length;\r\n                      const serviceTotal = service.price * serviceQuantity;\r\n\r\n                      return (\r\n                        <div\r\n                          key={service._id}\r\n                          className=\"d-flex justify-content-between align-items-center mb-1\"\r\n                        >\r\n                          <span>\r\n                            {service.quantity} x {service.name} (\r\n                            {selectedDates.length} days):\r\n                          </span>\r\n                          <span className=\"fw-bold\">\r\n                            {Utils.formatCurrency(serviceTotal)}\r\n                          </span>\r\n                        </div>\r\n                      );\r\n                    })}\r\n\r\n                    <div className=\"small mb-3\">\r\n                      <a\r\n                        className=\"text-blue text-decoration-none\"\r\n                        style={{ cursor: \"pointer\" }}\r\n                        onClick={() => {\r\n                          dispatch({\r\n                            type: SearchActions.SAVE_SELECTED_ROOMS,\r\n                            payload: {\r\n                              selectedRooms: selectedRooms,\r\n                              selectedServices: selectedServices,\r\n                              hotelDetail: hotelDetail,\r\n                            },\r\n                          });\r\n                          navigate(-1);\r\n                        }}\r\n                      >\r\n                        Change your selection\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"booking-divider mb-3\"\r\n                  style={{\r\n                    height: \"1px\",\r\n                    backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                    margin: \"15px 0\",\r\n                  }}\r\n                ></div>\r\n\r\n                <div className=\"promotion-section mb-3\">\r\n                  {/* Current applied promotion display */}\r\n                  {promotionDiscount > 0 ? (\r\n                    <Card\r\n                      className=\"promotion-applied mb-3\"\r\n                      style={{\r\n                        backgroundColor: \"rgba(40, 167, 69, 0.2)\",\r\n                        borderColor: \"#28a745\",\r\n                        border: \"2px solid #28a745\"\r\n                      }}\r\n                    >\r\n                      <Card.Body className=\"py-2\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                          <div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                              <FaTag className=\"text-success me-2\" />\r\n                              <span className=\"fw-bold text-success\">{promotionCode}</span>\r\n                            </div>\r\n                            <small className=\"text-success\">\r\n                              Save {Utils.formatCurrency(promotionDiscount)}\r\n                            </small>\r\n                          </div>\r\n                          <Button\r\n                            variant=\"outline-danger\"\r\n                            size=\"sm\"\r\n                            onClick={() => handleApplyPromotionFromModal({\r\n                              code: \"\",\r\n                              discount: 0,\r\n                              message: \"\",\r\n                              promotionId: null\r\n                            })}\r\n                            className=\"d-flex align-items-center\"\r\n                          >\r\n                            <FaTimes className=\"me-1\" />\r\n                            Remove\r\n                          </Button>\r\n                        </div>\r\n                      </Card.Body>\r\n                    </Card>\r\n                  ) : (\r\n                    <div className=\"text-center py-3 mb-3\" style={{\r\n                      border: \"2px dashed rgba(255,255,255,0.3)\",\r\n                      borderRadius: \"8px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.05)\"\r\n                    }}>\r\n                      <FaTag className=\"text-muted mb-2\" size={24} />\r\n                      <div className=\"text-muted small\">No promotion applied</div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Button to open promotion modal */}\r\n                  <Button\r\n                    variant=\"outline-light\"\r\n                    className=\"w-100 d-flex align-items-center justify-content-center\"\r\n                    onClick={() => setShowPromotionModal(true)}\r\n                    style={{\r\n                      borderStyle: \"dashed\",\r\n                      borderWidth: \"2px\",\r\n                      padding: \"12px\"\r\n                    }}\r\n                    disabled={isValidatingPromotion || isValidatingPromotionBeforeBooking}\r\n                  >\r\n                    <FaTag className=\"me-2\" />\r\n                    {isValidatingPromotion || isValidatingPromotionBeforeBooking ? \"Validating...\" : (promotionDiscount > 0 ? \"Change Promotion\" : \"Select Promotion\")}\r\n                  </Button>\r\n\r\n                  {/* Validation status indicator */}\r\n                  {(isValidatingPromotion || isValidatingPromotionBeforeBooking) && (\r\n                    <div className=\"text-center mt-2\">\r\n                      <small className=\"text-info\">\r\n                        <div className=\"spinner-border spinner-border-sm me-1\" role=\"status\">\r\n                          <span className=\"visually-hidden\">Loading...</span>\r\n                        </div>\r\n                        Checking promotion validity...\r\n                      </small>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Price breakdown section */}\r\n                <div className=\"price-breakdown\">\r\n                  <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                    <span>Subtotal:</span>\r\n                    <span className=\"fw-bold\">{Utils.formatCurrency(subtotal)}</span>\r\n                  </div>\r\n\r\n                  {promotionDiscount > 0 && (\r\n                    <div className=\"d-flex justify-content-between align-items-center mb-2\">\r\n                      <span className=\"text-success\">Discount:</span>\r\n                      <span className=\"fw-bold text-success\">-{Utils.formatCurrency(promotionDiscount)}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div\r\n                    className=\"booking-divider mb-2\"\r\n                    style={{\r\n                      height: \"1px\",\r\n                      backgroundColor: \"rgba(255,255,255,0.2)\",\r\n                      margin: \"10px 0\",\r\n                    }}\r\n                  ></div>\r\n\r\n                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                    <h5 className=\"text-danger mb-0\">\r\n                      Total: {Utils.formatCurrency(finalPrice)}\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"small\">Includes taxes and fees</div>\r\n                </div>\r\n              </Card>\r\n            </Col>\r\n\r\n            {/* Right Card - Customer Information */}\r\n            <Col md={5} lg={4}>\r\n              <Card\r\n                className=\"info-card\"\r\n                style={{\r\n                  backgroundColor: \"rgba(20, 30, 70, 0.85)\",\r\n                  borderRadius: \"10px\",\r\n                  padding: \"20px\",\r\n                  color: \"white\",\r\n                }}\r\n              >\r\n                <h4 className=\"mb-4\">Check your information</h4>\r\n\r\n                <Form>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Full name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={Auth.name}\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label>Email</Form.Label>\r\n                    <Form.Control\r\n                      type=\"email\"\r\n                      value={Auth.email}\r\n                      placeholder=\"<EMAIL>\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Phone</Form.Label>\r\n                    <Form.Control\r\n                      type=\"tel\"\r\n                      value={Auth.phoneNumber}\r\n                      placeholder=\"0912345678\"\r\n                      className=\"bg-transparent text-white\"\r\n                      style={{\r\n                        border: \"1px solid rgba(255,255,255,0.3)\",\r\n                        borderRadius: \"5px\",\r\n                      }}\r\n                    />\r\n                  </Form.Group>\r\n\r\n                  <Form.Group className=\"mb-4\">\r\n                    <Form.Label>Who are you booking for?</Form.Label>\r\n                    <div>\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"mainGuest\"\r\n                        label=\"I'm the main guest\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"mainGuest\"}\r\n                        onChange={() => setBookingFor(\"mainGuest\")}\r\n                        className=\"mb-2\"\r\n                      />\r\n                      <Form.Check\r\n                        type=\"radio\"\r\n                        id=\"someoneElse\"\r\n                        label=\"I'm booking for someone else\"\r\n                        name=\"bookingFor\"\r\n                        checked={bookingFor === \"someoneElse\"}\r\n                        onChange={() => setBookingFor(\"someoneElse\")}\r\n                      />\r\n                    </div>\r\n                  </Form.Group>\r\n\r\n                  <div className=\"text-center\">\r\n                    <Button\r\n                      className=\"px-4 py-2\"\r\n                      style={{\r\n                        borderRadius: \"10px\",\r\n                        backgroundColor: \"white\",\r\n                        color: \"#007bff\",\r\n                        border: \"none\",\r\n                        fontWeight: \"bold\",\r\n                      }}\r\n                      onClick={handleConfirmBooking}\r\n                      disabled={isCheckingHotelStatus || isValidatingPromotion || isValidatingPromotionBeforeBooking}\r\n                    >\r\n                      {isValidatingPromotionBeforeBooking ? \"Validating Promotion...\" :\r\n                       isCheckingHotelStatus ? \"Checking Hotel...\" : \"Booking\"}\r\n                    </Button>\r\n                    {/* Accept Confirmation Modal */}\r\n                    <ConfirmationModal\r\n                      show={showAcceptModal}\r\n                      onHide={() => setShowAcceptModal(false)}\r\n                      onConfirm={handleAccept}\r\n                      title=\"Confirm Acceptance\"\r\n                      message=\"Do you want to proceed with this booking confirmation?\"\r\n                      confirmButtonText=\"Accept\"\r\n                      type=\"accept\"\r\n                    />\r\n                  </div>\r\n                </Form>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        </Container>\r\n        <div>\r\n          <ChatBox />\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n      \r\n      {/* Promotion Modal */}\r\n      <PromotionModal\r\n        show={showPromotionModal}\r\n        onHide={() => setShowPromotionModal(false)}\r\n        totalPrice={subtotal}\r\n        onApplyPromotion={handleApplyPromotionFromModal}\r\n        currentPromotionId={promotionId}\r\n      />\r\n      \r\n      <HotelClosedModal\r\n        show={showModalStatusBooking}\r\n        onClose={() => {\r\n          setShowModalStatusBooking(false);\r\n        }}\r\n      />\r\n\r\n      {/* Promotion Error Modal */}\r\n      <PromotionErrorModal\r\n        show={showPromotionErrorModal}\r\n        onClose={() => {\r\n          setShowPromotionErrorModal(false);\r\n          setPromotionErrorMessage(\"\");\r\n          setInvalidPromotionCode(\"\");\r\n        }}\r\n        onSelectNewPromotion={() => {\r\n          setShowPromotionErrorModal(false);\r\n          setPromotionErrorMessage(\"\");\r\n          setInvalidPromotionCode(\"\");\r\n          setShowPromotionModal(true);\r\n        }}\r\n        errorMessage={promotionErrorMessage}\r\n        promotionCode={invalidPromotionCode}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookingCheckPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,UAAU,QACL,iBAAiB;AACxB,OAAO,sCAAsC;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AAClE,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,SAASC,cAAc,EAAEC,cAAc,QAAQ,sBAAsB;AACrE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,SAAS,MAAM,iCAAiC;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA;EAC7B,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAE3E,MAAMsC,IAAI,GAAGhB,cAAc,CAAEiB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAACA,IAAI,CAAC;EACvD,MAAME,iBAAiB,GAAGlB,cAAc,CACrCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACD,iBAC1B,CAAC;EACD,MAAME,kBAAkB,GAAGpB,cAAc,CACtCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACE,aAC1B,CAAC;EACD,MAAMC,yBAAyB,GAAGtB,cAAc,CAC7CiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACI,gBAC1B,CAAC;EACD,MAAMC,oBAAoB,GAAGxB,cAAc,CACxCiB,KAAK,IAAKA,KAAK,CAACE,MAAM,CAACM,WAC1B,CAAC;EACD,MAAMC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM+B,QAAQ,GAAG1B,cAAc,CAAC,CAAC;EACjC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,WAAW,CAAC;;EAEzD;EACA,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAAC4D,WAAW,EAAEC,cAAc,CAAC,GAAG7D,QAAQ,CAAC;IAC7C2C,aAAa,EAAED,kBAAkB,IAAI,EAAE;IACvCG,gBAAgB,EAAED,yBAAyB,IAAI,EAAE;IACjDG,WAAW,EAAED,oBAAoB,IAAI,IAAI;IACzCgB,UAAU,EAAEtB;EACd,CAAC,CAAC;EAEF,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACmE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACqE,kCAAkC,EAAEC,qCAAqC,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnG,MAAM,CAACuE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwE,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGN,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC;MAC5DjB,cAAc,CAACkB,cAAc,CAAC;;MAE9B;MACA9B,QAAQ,CAAC;QACP+B,IAAI,EAAErD,aAAa,CAACsD,mBAAmB;QACvCC,OAAO,EAAE;UACPvC,aAAa,EAAEoC,cAAc,CAACpC,aAAa;UAC3CE,gBAAgB,EAAEkC,cAAc,CAAClC,gBAAgB;UACjDE,WAAW,EAAEgC,cAAc,CAAChC;QAC9B;MACF,CAAC,CAAC;IACJ;IACAiB,eAAe,CAAC,IAAI,CAAC;IACrBQ,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACvB,QAAQ,CAAC,CAAC;;EAEd;EACAhD,SAAS,CAAC,MAAM;IACd,IAAI8D,YAAY,EAAE;MAChB,MAAMoB,KAAK,GAAGT,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC;MAC3E,IAAIM,KAAK,EAAE;QACT;QACA,MAAMC,SAAS,GAAGD,KAAK,CAACC,SAAS,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;QACvC,MAAMI,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;QAEjC,IAAID,QAAQ,GAAGC,WAAW,EAAE;UAC1B;UACAC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnDrC,gBAAgB,CAAC8B,KAAK,CAAC/B,aAAa,IAAI,EAAE,CAAC;UAC3CG,oBAAoB,CAAC4B,KAAK,CAAC7B,iBAAiB,IAAI,CAAC,CAAC;UAClDG,mBAAmB,CAAC,yBAAyB,CAAC;UAC9CE,cAAc,CAACwB,KAAK,CAACzB,WAAW,IAAI,IAAI,CAAC;QAC3C,CAAC,MAAM;UACLL,gBAAgB,CAAC8B,KAAK,CAAC/B,aAAa,IAAI,EAAE,CAAC;UAC3CG,oBAAoB,CAAC4B,KAAK,CAAC7B,iBAAiB,IAAI,CAAC,CAAC;UAClDG,mBAAmB,CAAC0B,KAAK,CAAC3B,gBAAgB,IAAI,EAAE,CAAC;UACjDG,cAAc,CAACwB,KAAK,CAACzB,WAAW,IAAI,IAAI,CAAC;QAC3C;MACF;IACF;EACF,CAAC,EAAE,CAACK,YAAY,CAAC,CAAC;;EAElB;EACA9D,SAAS,CAAC,MAAM;IACd,IAAI8D,YAAY,EAAE;MAAE;MAClBa,cAAc,CAACe,OAAO,CACpB,eAAe,EACfjB,IAAI,CAACkB,SAAS,CAAC;QACbxC,aAAa;QACbE,iBAAiB;QACjBE,gBAAgB;QAChBE,WAAW;QACX0B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE;MACzB,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CAAClC,aAAa,EAAEE,iBAAiB,EAAEE,gBAAgB,EAAEE,WAAW,EAAEK,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAMpB,aAAa,GAAGiB,WAAW,CAACjB,aAAa;EAC/C,MAAME,gBAAgB,GAAGe,WAAW,CAACf,gBAAgB;EACrD,MAAME,WAAW,GAAGa,WAAW,CAACb,WAAW;EAC3C,MAAMe,UAAU,GAAGF,WAAW,CAACE,UAAU;;EAEzC;EACA,MAAM+B,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,OAAO,GAAG,IAAIT,IAAI,CAACvB,UAAU,CAACiC,WAAW,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIX,IAAI,CAACvB,UAAU,CAACmC,YAAY,CAAC;IAClD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,GAAGF,OAAO,CAAC;IAC7C,MAAMO,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB,CAAC;EAED,MAAME,YAAY,GAAGV,qBAAqB,CAAC,CAAC;;EAE5C;EACA,MAAMW,cAAc,GAAG7D,aAAa,CAAC8D,MAAM,CACzC,CAACC,KAAK,EAAE;IAAEC,IAAI;IAAEC;EAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;EACD,MAAMO,iBAAiB,GAAGjE,gBAAgB,CAAC4D,MAAM,CAAC,CAACC,KAAK,EAAEK,OAAO,KAAK;IACpE,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;IACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClC,MAAM;IAC/D,OAAO4B,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;EAChD,CAAC,EAAE,CAAC,CAAC;EACL,MAAME,QAAQ,GAAGX,cAAc,GAAGM,iBAAiB;EACnD,MAAMM,UAAU,GAAGjB,IAAI,CAACkB,GAAG,CAACF,QAAQ,GAAG7D,iBAAiB,EAAE,CAAC,CAAC;;EAE5D;EACArD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8D,YAAY,IAAI,CAACX,aAAa,IAAI,CAACM,WAAW,IAAIJ,iBAAiB,KAAK,CAAC,EAAE;;IAEhF;IACA,MAAMgE,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCtD,wBAAwB,CAAC,IAAI,CAAC;QAC9B,IAAI;UACF,MAAMuD,GAAG,GAAG,MAAMvH,KAAK,CAACwH,IAAI,CAAC,4CAA4C,EAAE;YACzEC,IAAI,EAAEvE,aAAa;YACnBwE,WAAW,EAAET;UACf,CAAC,CAAC;UAEF,IAAI,CAACM,GAAG,CAACI,IAAI,CAACC,KAAK,IAAIL,GAAG,CAACI,IAAI,CAACE,QAAQ,KAAKzE,iBAAiB,EAAE;YAC9D;YACAD,gBAAgB,CAAC,EAAE,CAAC;YACpBE,oBAAoB,CAAC,CAAC,CAAC;YACvBE,mBAAmB,CAAC,qDAAqD,CAAC;YAC1EE,cAAc,CAAC,IAAI,CAAC;YACpBiB,cAAc,CAACoD,UAAU,CAAC,eAAe,CAAC;UAC5C;QACF,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ;UACA5E,gBAAgB,CAAC,EAAE,CAAC;UACpBE,oBAAoB,CAAC,CAAC,CAAC;UACvBE,mBAAmB,CAAC,8BAA8B,CAAC;UACnDE,cAAc,CAAC,IAAI,CAAC;UACpBiB,cAAc,CAACoD,UAAU,CAAC,eAAe,CAAC;QAC5C,CAAC,SAAS;UACR9D,wBAAwB,CAAC,KAAK,CAAC;QACjC;MACF,CAAC;MAEDsD,iBAAiB,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMU,YAAY,CAACZ,SAAS,CAAC;EACtC,CAAC,EAAE,CAACvD,YAAY,EAAEoD,QAAQ,EAAE/D,aAAa,EAAEM,WAAW,EAAEJ,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAE7E;EACA,MAAM6E,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAM1D,YAAY,GAAGC,IAAI,CAACC,KAAK,CAC7BC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAC5C,CAAC;IACD,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B;MACAL,YAAY,CAAC2D,GAAG,CAAC,CAAC;MAClBxD,cAAc,CAACe,OAAO,CAAC,cAAc,EAAEjB,IAAI,CAACkB,SAAS,CAACnB,YAAY,CAAC,CAAC;IACtE;IACAzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMqF,UAAU,GAAGA,CAAC;IAAEC;EAAO,CAAC,KAAK;IACjC,oBACEvG,OAAA;MAAKwG,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC1BA,KAAK,GAAGN,MAAM,gBACZvG,OAAA,CAACrB,MAAM;QAAa6H,SAAS,EAAC;MAAa,GAA9BK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA2B,CAAC,gBAE9CjH,OAAA,CAACpB,SAAS;QAAa4H,SAAS,EAAC;MAAM,GAAvBK,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAE7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpJ,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqJ,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGtJ,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACuJ,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACyJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;;EAEpE;EACA,MAAM2J,6BAA6B,GAAIC,aAAa,IAAK;IACvDvG,gBAAgB,CAACuG,aAAa,CAACjC,IAAI,CAAC;IACpCpE,oBAAoB,CAACqG,aAAa,CAAC7B,QAAQ,CAAC;IAC5CtE,mBAAmB,CAACmG,aAAa,CAACC,OAAO,CAAC;IAC1ClG,cAAc,CAACiG,aAAa,CAAClG,WAAW,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMoG,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IACjD,IAAI,CAAC1G,aAAa,IAAI,CAACM,WAAW,IAAIJ,iBAAiB,KAAK,CAAC,EAAE;MAC7D,OAAO;QAAEwE,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;IAC1B;IAEAxD,qCAAqC,CAAC,IAAI,CAAC;IAC3C,IAAI;MACF,MAAMmD,GAAG,GAAG,MAAMvH,KAAK,CAACwH,IAAI,CAAC,4CAA4C,EAAE;QACzEC,IAAI,EAAEvE,aAAa;QACnBwE,WAAW,EAAET;MACf,CAAC,CAAC;MAEF7C,qCAAqC,CAAC,KAAK,CAAC;MAE5C,IAAI,CAACmD,GAAG,CAACI,IAAI,CAACC,KAAK,EAAE;QACnB,OAAO;UACLA,KAAK,EAAE,KAAK;UACZ+B,OAAO,EAAEpC,GAAG,CAACI,IAAI,CAACgC,OAAO,IAAI;QAC/B,CAAC;MACH;MAEA,IAAIpC,GAAG,CAACI,IAAI,CAACE,QAAQ,KAAKzE,iBAAiB,EAAE;QAC3C,OAAO;UACLwE,KAAK,EAAE,KAAK;UACZ+B,OAAO,EAAE;QACX,CAAC;MACH;MAEA,OAAO;QAAE/B,KAAK,EAAE;MAAK,CAAC;IACxB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZ3D,qCAAqC,CAAC,KAAK,CAAC;MAC5C,OAAO;QACLwD,KAAK,EAAE,KAAK;QACZ+B,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAME,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC9F,wBAAwB,CAAC,IAAI,CAAC;MAC9BnB,QAAQ,CAAC;QACP+B,IAAI,EAAEpD,YAAY,CAACuI,kBAAkB;QACrCjF,OAAO,EAAE;UACPkF,OAAO,EAAErH,WAAW,CAACsH,GAAG;UACxBC,MAAM,EAAEhI,IAAI,CAAC+H,GAAG;UAChBE,SAAS,EAAGC,KAAK,IAAK;YACpBpG,wBAAwB,CAAC,KAAK,CAAC;YAC/B,IAAIoG,KAAK,CAACC,WAAW,KAAK,QAAQ,EAAE;cAClCR,OAAO,CAACO,KAAK,CAAC;YAChB,CAAC,MAAM;cACLN,MAAM,CAAC,IAAIQ,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAClD;UACF,CAAC;UACDC,QAAQ,EAAGC,KAAK,IAAK;YACnBxG,wBAAwB,CAAC,KAAK,CAAC;YAC/B8F,MAAM,CAAC,IAAIQ,KAAK,CAACE,KAAK,IAAI,8BAA8B,CAAC,CAAC;UAC5D,CAAC;UACDC,OAAO,EAAGD,KAAK,IAAK;YAClBxG,wBAAwB,CAAC,KAAK,CAAC;YAC/B8F,MAAM,CAAC,IAAIQ,KAAK,CAAC,0CAA0C,CAAC,CAAC;UAC/D;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACA,MAAMC,mBAAmB,GAAG,MAAMjB,8BAA8B,CAAC,CAAC;MAClE,IAAI,CAACiB,mBAAmB,CAACjD,KAAK,EAAE;QAC9B;QACA0B,wBAAwB,CAACuB,mBAAmB,CAAClB,OAAO,CAAC;QACrDH,uBAAuB,CAACtG,aAAa,CAAC;;QAEtC;QACAC,gBAAgB,CAAC,EAAE,CAAC;QACpBE,oBAAoB,CAAC,CAAC,CAAC;QACvBE,mBAAmB,CAAC,EAAE,CAAC;QACvBE,cAAc,CAAC,IAAI,CAAC;QACpBiB,cAAc,CAACoD,UAAU,CAAC,eAAe,CAAC;;QAE1C;QACAsB,0BAA0B,CAAC,IAAI,CAAC;QAChC;MACF;;MAEA;MACA,MAAMkB,KAAK,GAAG,MAAMT,6BAA6B,CAAC,CAAC;MACnDtE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE8E,KAAK,CAAC;MAClD,MAAMhE,cAAc,GAAG7D,aAAa,CAAC8D,MAAM,CACzC,CAACC,KAAK,EAAE;QAAEC,IAAI;QAAEC;MAAO,CAAC,KACtBF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EAC5C,CACF,CAAC;MAED,MAAMO,iBAAiB,GAAGjE,gBAAgB,CAAC4D,MAAM,CAC/C,CAACC,KAAK,EAAEK,OAAO,KAAK;QAClB,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;QACjD,MAAMC,eAAe,GAAGF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClC,MAAM;QAC/D,OAAO4B,KAAK,GAAGK,OAAO,CAACF,KAAK,GAAGI,eAAe;MAChD,CAAC,EACD,CACF,CAAC;MAED,MAAM+D,eAAe,GAAGxE,cAAc,GAAGM,iBAAiB;MAE1D,MAAMmE,MAAM,GAAG;QACbb,OAAO,EAAErH,WAAW,CAACsH,GAAG;QACxBa,YAAY,EAAEpH,UAAU,CAACmC,YAAY;QACrCkF,WAAW,EAAErH,UAAU,CAACiC,WAAW;QACnCqF,UAAU,EAAEJ,eAAe;QAAE;QAC7B5D,UAAU,EAAEA,UAAU;QAAE;QACxBiE,WAAW,EAAE1I,aAAa,CAAC+F,GAAG,CAAC,CAAC;UAAE/B,IAAI;UAAEC;QAAO,CAAC,MAAM;UACpDD,IAAI,EAAE;YACJ0D,GAAG,EAAE1D,IAAI,CAAC0D;UACZ,CAAC;UACDzD,MAAM,EAAEA;QACV,CAAC,CAAC,CAAC;QACH0E,cAAc,EAAEzI,gBAAgB,CAAC6F,GAAG,CAAE3B,OAAO;UAAA,IAAAwE,qBAAA;UAAA,OAAM;YACjDlB,GAAG,EAAEtD,OAAO,CAACsD,GAAG;YAChBnD,QAAQ,EACNH,OAAO,CAACG,QAAQ,IAAI,EAAAqE,qBAAA,GAAAxE,OAAO,CAACC,aAAa,cAAAuE,qBAAA,uBAArBA,qBAAA,CAAuBzG,MAAM,KAAI,CAAC,CAAC;YACzD0G,UAAU,EAAEzE,OAAO,CAACC,aAAa,IAAI;UACvC,CAAC;QAAA,CAAC,CAAC;QACH;QACA,IAAItD,WAAW,IAAI;UAAEA;QAAY,CAAC,CAAC;QACnC,IAAIJ,iBAAiB,GAAG,CAAC,IAAI;UAAEA;QAAkB,CAAC;MACpD,CAAC;MAEDmC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEuF,MAAM,CAAC;;MAEjC;MACA,MAAMQ,+BAA+B,GAAIC,aAAa,IAAK;QACzD,IAAIA,aAAa,EAAE;UACjB,MAAMjH,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;UAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,EAAE;YAC3BL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC4G,aAAa,GAAGA,aAAa;YACnE9G,cAAc,CAACe,OAAO,CAAC,cAAc,EAAEjB,IAAI,CAACkB,SAAS,CAACnB,YAAY,CAAC,CAAC;UACtE;QACF;MACF,CAAC;MACD,IAAI;QACF,IAAIiH,aAAa,GAAG,IAAI;QACxB,MAAMjH,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC/E,IAAIJ,YAAY,CAACK,MAAM,GAAG,CAAC,IAAIL,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC4G,aAAa,EAAE;UAClFA,aAAa,GAAGjH,YAAY,CAACA,YAAY,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC4G,aAAa;QACrE;QACA,MAAMC,QAAQ,GAAG,MAAMlK,SAAS,CAACmK,cAAc,CAAC;UAAE,GAAGX,MAAM;UAAES;QAAc,CAAC,CAAC;QAC7EjG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiG,QAAQ,CAAC;QACrC,IAAI,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAC5BN,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAG,cAAA,GAARH,QAAQ,CAAE9D,IAAI,cAAAiE,cAAA,wBAAAC,qBAAA,GAAdD,cAAA,CAAgBG,iBAAiB,cAAAF,qBAAA,uBAAjCA,qBAAA,CAAmC1B,GAAG;UACtDoB,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMQ,mBAAmB,GAAGR,aAAa;UACzC,MAAMS,gBAAgB,GAAG,MAAM1K,SAAS,CAAC2K,gBAAgB,CACvDF,mBACF,CAAC;UACDzG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEyG,gBAAgB,CAAC;UACrD,MAAME,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAH,qBAAA,GAAhBG,gBAAgB,CAAEtE,IAAI,cAAAmE,qBAAA,uBAAtBA,qBAAA,CAAwBM,UAAU;UACrD,IAAID,UAAU,EAAE;YACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM,IAAI,CAAAV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;UAAA,IAAAa,eAAA,EAAAC,qBAAA,EAAAC,sBAAA;UACnClB,aAAa,GAAGC,QAAQ,aAARA,QAAQ,wBAAAe,eAAA,GAARf,QAAQ,CAAE9D,IAAI,cAAA6E,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBG,WAAW,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6BtC,GAAG;UAChDoB,+BAA+B,CAACC,aAAa,CAAC;UAC9C,MAAMS,gBAAgB,GAAG,MAAM1K,SAAS,CAAC2K,gBAAgB,CACvDV,aACF,CAAC;UACD,MAAMW,UAAU,GAAGF,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAEtE,IAAI,cAAA+E,sBAAA,uBAAtBA,sBAAA,CAAwBN,UAAU;UACrD,IAAID,UAAU,EAAE;YACdE,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGJ,UAAU;UACnC;QACF,CAAC,MAAM;UACL5G,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACrC;MACF,CAAC,CAAC,OAAOkF,KAAK,EAAE;QACdnF,OAAO,CAACmF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C5H,QAAQ,CAAC/B,OAAO,CAAC6L,SAAS,CAAC;MAC7B;IACR,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdnF,OAAO,CAACmF,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDvI,yBAAyB,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAM0K,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMvG,cAAc,GAAG7D,aAAa,CAAC8D,MAAM,CACzC,CAACC,KAAK,EAAE;MAAEC,IAAI;MAAEC;IAAO,CAAC,KAAKF,KAAK,GAAGC,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YAAY,EACvE,CACF,CAAC;IAED,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtB;MACA,MAAMsE,aAAa,CAAC,CAAC;;MAErB;MACA;MACA7H,QAAQ,CAAC;QACP+B,IAAI,EAAErD,aAAa,CAACsD,mBAAmB;QACvCC,OAAO,EAAE;UACPvC,aAAa,EAAE,EAAE;UACjBE,gBAAgB,EAAE,EAAE;UACpBE,WAAW,EAAEA;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMiK,oBAAoB,GAAGA,CAAA,KAAM;IACjC9D,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM+D,cAAc,GAAIrG,MAAM,IAAK;IACjC,IAAIA,MAAM,KAAKsG,SAAS,IAAItG,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;IACxD,OAAO,IAAIuG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAAC7G,MAAM,CAAC;EACnB,CAAC;;EAED;EACA,IAAIrC,gBAAgB,IAAK,CAACxB,WAAW,IAAI,CAACgB,YAAa,EAAE;IACvD,oBACEhC,OAAA;MACEwG,SAAS,EAAC,kDAAkD;MAC5D8E,KAAK,EAAE;QAAEK,MAAM,EAAE;MAAQ,CAAE;MAAAlF,QAAA,eAE3BzG,OAAA;QAAKwG,SAAS,EAAC,6BAA6B;QAACoF,IAAI,EAAC,QAAQ;QAAAnF,QAAA,eACxDzG,OAAA;UAAMwG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACjG,WAAW,IAAIgB,YAAY,EAAE;IAChCf,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,OAAO,IAAI;EACb;EAEA,oBACEjB,OAAA;IACEwG,SAAS,EAAC,+BAA+B;IACzC8E,KAAK,EAAE;MACLO,eAAe,EAAE,OAAO9M,MAAM,GAAG;MACjC+M,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAtF,QAAA,gBAEFzG,OAAA,CAAChB,MAAM;MAAA8H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVjH,OAAA;MACEwG,SAAS,EAAC,8EAA8E;MACxF8E,KAAK,EAAE;QAAEU,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAAxF,QAAA,gBAErDzG,OAAA,CAAC5B,SAAS;QAACoI,SAAS,EAAC,MAAM;QAAAC,QAAA,eACzBzG,OAAA,CAAC3B,GAAG;UAACmI,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBAErCzG,OAAA,CAAC1B,GAAG;YAAC4N,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA1F,QAAA,eAChBzG,OAAA,CAACzB,IAAI;cACHiI,SAAS,EAAC,yBAAyB;cACnC8E,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE;cAChB,CAAE;cAAA9F,QAAA,gBAEFzG,OAAA;gBACEwG,SAAS,EAAC,YAAY;gBACtB8E,KAAK,EAAE;kBACLkB,cAAc,EAAE,YAAY;kBAC5BC,YAAY,EAAE;gBAChB,CAAE;gBAAAhG,QAAA,eAEFzG,OAAA,CAACsG,UAAU;kBAACC,MAAM,EAAEvF,WAAW,CAAC0L;gBAAK;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAENjH,OAAA;gBAAIwG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAAtG,qBAAA,GAC5Ba,WAAW,CAAC2L,SAAS,cAAAxM,qBAAA,cAAAA,qBAAA,GAAI;cAAE;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAELjH,OAAA;gBAAGwG,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GAAArG,oBAAA,GACpCY,WAAW,CAAC4L,OAAO,cAAAxM,oBAAA,cAAAA,oBAAA,GAAI;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEJjH,OAAA;gBACEwG,SAAS,EAAC,sBAAsB;gBAChC8E,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPjH,OAAA;gBAAIwG,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE7CjH,OAAA,CAAC3B,GAAG;gBAACmI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzG,OAAA,CAAC1B,GAAG;kBAACwO,EAAE,EAAE,CAAE;kBAAArG,QAAA,eACTzG,OAAA;oBAAKwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,gBACtBzG,OAAA;sBACEwG,SAAS,EAAC,oBAAoB;sBAC9B8E,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAtG,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNjH,OAAA;sBAAKwG,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBhH,KAAK,CAACuN,OAAO,CAACjL,UAAU,CAACiC,WAAW,EAAE,CAAC;oBAAC;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNjH,OAAA,CAAC1B,GAAG;kBAACwO,EAAE,EAAE,CAAE;kBAAArG,QAAA,eACTzG,OAAA;oBAAKwG,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBzG,OAAA;sBACEwG,SAAS,EAAC,oBAAoB;sBAC9B8E,KAAK,EAAE;wBAAEyB,QAAQ,EAAE;sBAAG,CAAE;sBAAAtG,QAAA,EACzB;oBAED;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNjH,OAAA;sBAAKwG,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBhH,KAAK,CAACuN,OAAO,CAACjL,UAAU,CAACmC,YAAY,EAAE,CAAC;oBAAC;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjH,OAAA;gBAAKwG,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BzG,OAAA;kBAAKwG,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDzG,OAAA;oBAAAyG,QAAA,EAAM;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClCjH,OAAA;oBAAMwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,GAAEjC,YAAY,EAAC,QAAM;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAAC,GAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNjH,OAAA;kBAAKwG,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDzG,OAAA;oBAAAyG,QAAA,EAAM;kBAAuB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCjH,OAAA;oBAAMwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,GACtB1E,UAAU,CAACkL,MAAM,EAAC,YAAU,EAAClL,UAAU,CAACmL,SAAS,EAAE,GAAG,EAAC,WAE1D;kBAAA;oBAAApG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjH,OAAA;gBACEwG,SAAS,EAAC,sBAAsB;gBAChC8E,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPjH,OAAA;gBAAKwG,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCzG,OAAA;kBAAIwG,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAY;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAErCrG,aAAa,CAAC+F,GAAG,CAAC,CAAC;kBAAE/B,IAAI;kBAAEC;gBAAO,CAAC,kBAClC7E,OAAA;kBAEEwG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElEzG,OAAA;oBAAAyG,QAAA,GACG5B,MAAM,EAAC,KAAG,EAACD,IAAI,CAACuI,IAAI,EAAC,IAAE,EAAC3I,YAAY,EAAC,SACxC;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACPjH,OAAA;oBAAMwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACtBhH,KAAK,CAACyL,cAAc,CACnBtG,IAAI,CAACE,KAAK,GAAGD,MAAM,GAAGL,YACxB;kBAAC;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAVFrC,IAAI,CAAC0D,GAAG;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWV,CACN,CAAC,eAEFjH,OAAA;kBAAKwG,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBzG,OAAA;oBACEwG,SAAS,EAAC,gCAAgC;oBAC1C8E,KAAK,EAAE;sBAAE8B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEjH,sBAAuB;oBAAAK,QAAA,EACjC;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLnG,gBAAgB,CAACiC,MAAM,GAAG,CAAC,iBAC1B/C,OAAA;gBAAKwG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCzG,OAAA;kBAAIwG,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE1CnG,gBAAgB,CAAC6F,GAAG,CAAE3B,OAAO,IAAK;kBACjC,MAAMC,aAAa,GAAGD,OAAO,CAACC,aAAa,IAAI,EAAE;kBACjD,MAAMC,eAAe,GACnBF,OAAO,CAACG,QAAQ,GAAGF,aAAa,CAAClC,MAAM;kBACzC,MAAMuK,YAAY,GAAGtI,OAAO,CAACF,KAAK,GAAGI,eAAe;kBAEpD,oBACElF,OAAA;oBAEEwG,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,gBAElEzG,OAAA;sBAAAyG,QAAA,GACGzB,OAAO,CAACG,QAAQ,EAAC,KAAG,EAACH,OAAO,CAACmI,IAAI,EAAC,IACnC,EAAClI,aAAa,CAAClC,MAAM,EAAC,SACxB;oBAAA;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPjH,OAAA;sBAAMwG,SAAS,EAAC,SAAS;sBAAAC,QAAA,EACtBhH,KAAK,CAACyL,cAAc,CAACoC,YAAY;oBAAC;sBAAAxG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA,GATFjC,OAAO,CAACsD,GAAG;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUb,CAAC;gBAEV,CAAC,CAAC,eAEFjH,OAAA;kBAAKwG,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBzG,OAAA;oBACEwG,SAAS,EAAC,gCAAgC;oBAC1C8E,KAAK,EAAE;sBAAE8B,MAAM,EAAE;oBAAU,CAAE;oBAC7BC,OAAO,EAAEA,CAAA,KAAM;sBACbnM,QAAQ,CAAC;wBACP+B,IAAI,EAAErD,aAAa,CAACsD,mBAAmB;wBACvCC,OAAO,EAAE;0BACPvC,aAAa,EAAEA,aAAa;0BAC5BE,gBAAgB,EAAEA,gBAAgB;0BAClCE,WAAW,EAAEA;wBACf;sBACF,CAAC,CAAC;sBACFC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBACd,CAAE;oBAAAwF,QAAA,EACH;kBAED;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDjH,OAAA;gBACEwG,SAAS,EAAC,sBAAsB;gBAChC8E,KAAK,EAAE;kBACLK,MAAM,EAAE,KAAK;kBACbS,eAAe,EAAE,uBAAuB;kBACxCS,MAAM,EAAE;gBACV;cAAE;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEPjH,OAAA;gBAAKwG,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAEpClF,iBAAiB,GAAG,CAAC,gBACpBvB,OAAA,CAACzB,IAAI;kBACHiI,SAAS,EAAC,wBAAwB;kBAClC8E,KAAK,EAAE;oBACLc,eAAe,EAAE,wBAAwB;oBACzCmB,WAAW,EAAE,SAAS;oBACtBC,MAAM,EAAE;kBACV,CAAE;kBAAA/G,QAAA,eAEFzG,OAAA,CAACzB,IAAI,CAACkP,IAAI;oBAACjH,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzBzG,OAAA;sBAAKwG,SAAS,EAAC,mDAAmD;sBAAAC,QAAA,gBAChEzG,OAAA;wBAAAyG,QAAA,gBACEzG,OAAA;0BAAKwG,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,gBACxCzG,OAAA,CAACnB,KAAK;4BAAC2H,SAAS,EAAC;0BAAmB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCjH,OAAA;4BAAMwG,SAAS,EAAC,sBAAsB;4BAAAC,QAAA,EAAEpF;0BAAa;4BAAAyF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D,CAAC,eACNjH,OAAA;0BAAOwG,SAAS,EAAC,cAAc;0BAAAC,QAAA,GAAC,OACzB,EAAChH,KAAK,CAACyL,cAAc,CAAC3J,iBAAiB,CAAC;wBAAA;0BAAAuF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACNjH,OAAA,CAACvB,MAAM;wBACLiP,OAAO,EAAC,gBAAgB;wBACxBC,IAAI,EAAC,IAAI;wBACTN,OAAO,EAAEA,CAAA,KAAMzF,6BAA6B,CAAC;0BAC3ChC,IAAI,EAAE,EAAE;0BACRI,QAAQ,EAAE,CAAC;0BACX8B,OAAO,EAAE,EAAE;0BACXnG,WAAW,EAAE;wBACf,CAAC,CAAE;wBACH6E,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,gBAErCzG,OAAA,CAAClB,OAAO;0BAAC0H,SAAS,EAAC;wBAAM;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAE9B;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,gBAEPjH,OAAA;kBAAKwG,SAAS,EAAC,uBAAuB;kBAAC8E,KAAK,EAAE;oBAC5CkC,MAAM,EAAE,kCAAkC;oBAC1CnB,YAAY,EAAE,KAAK;oBACnBD,eAAe,EAAE;kBACnB,CAAE;kBAAA3F,QAAA,gBACAzG,OAAA,CAACnB,KAAK;oBAAC2H,SAAS,EAAC,iBAAiB;oBAACmH,IAAI,EAAE;kBAAG;oBAAA7G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CjH,OAAA;oBAAKwG,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CACN,eAGDjH,OAAA,CAACvB,MAAM;kBACLiP,OAAO,EAAC,eAAe;kBACvBlH,SAAS,EAAC,wDAAwD;kBAClE6G,OAAO,EAAEA,CAAA,KAAMhG,qBAAqB,CAAC,IAAI,CAAE;kBAC3CiE,KAAK,EAAE;oBACLsC,WAAW,EAAE,QAAQ;oBACrBC,WAAW,EAAE,KAAK;oBAClBvB,OAAO,EAAE;kBACX,CAAE;kBACFwB,QAAQ,EAAE5L,qBAAqB,IAAII,kCAAmC;kBAAAmE,QAAA,gBAEtEzG,OAAA,CAACnB,KAAK;oBAAC2H,SAAS,EAAC;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACzB/E,qBAAqB,IAAII,kCAAkC,GAAG,eAAe,GAAIf,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,kBAAmB;gBAAA;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5I,CAAC,EAGR,CAAC/E,qBAAqB,IAAII,kCAAkC,kBAC3DtC,OAAA;kBAAKwG,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,eAC/BzG,OAAA;oBAAOwG,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBAC1BzG,OAAA;sBAAKwG,SAAS,EAAC,uCAAuC;sBAACoF,IAAI,EAAC,QAAQ;sBAAAnF,QAAA,eAClEzG,OAAA;wBAAMwG,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,EAAC;sBAAU;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,kCAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNjH,OAAA;gBAAKwG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BzG,OAAA;kBAAKwG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrEzG,OAAA;oBAAAyG,QAAA,EAAM;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtBjH,OAAA;oBAAMwG,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEhH,KAAK,CAACyL,cAAc,CAAC9F,QAAQ;kBAAC;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,EAEL1F,iBAAiB,GAAG,CAAC,iBACpBvB,OAAA;kBAAKwG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrEzG,OAAA;oBAAMwG,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/CjH,OAAA;oBAAMwG,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAAC,GAAC,EAAChH,KAAK,CAACyL,cAAc,CAAC3J,iBAAiB,CAAC;kBAAA;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CACN,eAEDjH,OAAA;kBACEwG,SAAS,EAAC,sBAAsB;kBAChC8E,KAAK,EAAE;oBACLK,MAAM,EAAE,KAAK;oBACbS,eAAe,EAAE,uBAAuB;oBACxCS,MAAM,EAAE;kBACV;gBAAE;kBAAA/F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEPjH,OAAA;kBAAKwG,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChEzG,OAAA;oBAAIwG,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,SACxB,EAAChH,KAAK,CAACyL,cAAc,CAAC7F,UAAU,CAAC;kBAAA;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNjH,OAAA;kBAAKwG,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNjH,OAAA,CAAC1B,GAAG;YAAC4N,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA1F,QAAA,eAChBzG,OAAA,CAACzB,IAAI;cACHiI,SAAS,EAAC,WAAW;cACrB8E,KAAK,EAAE;gBACLc,eAAe,EAAE,wBAAwB;gBACzCC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfyB,KAAK,EAAE;cACT,CAAE;cAAAtH,QAAA,gBAEFzG,OAAA;gBAAIwG,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEhDjH,OAAA,CAACxB,IAAI;gBAAAiI,QAAA,gBACHzG,OAAA,CAACxB,IAAI,CAACwP,KAAK;kBAACxH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzG,OAAA,CAACxB,IAAI,CAACyP,KAAK;oBAAAxH,QAAA,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClCjH,OAAA,CAACxB,IAAI,CAAC0P,OAAO;oBACXjL,IAAI,EAAC,MAAM;oBACXkL,KAAK,EAAE5N,IAAI,CAAC4M,IAAK;oBACjB3G,SAAS,EAAC,2BAA2B;oBACrC8E,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbjH,OAAA,CAACxB,IAAI,CAACwP,KAAK;kBAACxH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzG,OAAA,CAACxB,IAAI,CAACyP,KAAK;oBAAAxH,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BjH,OAAA,CAACxB,IAAI,CAAC0P,OAAO;oBACXjL,IAAI,EAAC,OAAO;oBACZkL,KAAK,EAAE5N,IAAI,CAAC6N,KAAM;oBAClBC,WAAW,EAAC,sBAAsB;oBAClC7H,SAAS,EAAC,2BAA2B;oBACrC8E,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbjH,OAAA,CAACxB,IAAI,CAACwP,KAAK;kBAACxH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzG,OAAA,CAACxB,IAAI,CAACyP,KAAK;oBAAAxH,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9BjH,OAAA,CAACxB,IAAI,CAAC0P,OAAO;oBACXjL,IAAI,EAAC,KAAK;oBACVkL,KAAK,EAAE5N,IAAI,CAAC+N,WAAY;oBACxBD,WAAW,EAAC,YAAY;oBACxB7H,SAAS,EAAC,2BAA2B;oBACrC8E,KAAK,EAAE;sBACLkC,MAAM,EAAE,iCAAiC;sBACzCnB,YAAY,EAAE;oBAChB;kBAAE;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eAEbjH,OAAA,CAACxB,IAAI,CAACwP,KAAK;kBAACxH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBAC1BzG,OAAA,CAACxB,IAAI,CAACyP,KAAK;oBAAAxH,QAAA,EAAC;kBAAwB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjDjH,OAAA;oBAAAyG,QAAA,gBACEzG,OAAA,CAACxB,IAAI,CAAC+P,KAAK;sBACTtL,IAAI,EAAC,OAAO;sBACZuL,EAAE,EAAC,WAAW;sBACdC,KAAK,EAAC,oBAAoB;sBAC1BtB,IAAI,EAAC,YAAY;sBACjBuB,OAAO,EAAEvN,UAAU,KAAK,WAAY;sBACpCwN,QAAQ,EAAEA,CAAA,KAAMvN,aAAa,CAAC,WAAW,CAAE;sBAC3CoF,SAAS,EAAC;oBAAM;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACFjH,OAAA,CAACxB,IAAI,CAAC+P,KAAK;sBACTtL,IAAI,EAAC,OAAO;sBACZuL,EAAE,EAAC,aAAa;sBAChBC,KAAK,EAAC,8BAA8B;sBACpCtB,IAAI,EAAC,YAAY;sBACjBuB,OAAO,EAAEvN,UAAU,KAAK,aAAc;sBACtCwN,QAAQ,EAAEA,CAAA,KAAMvN,aAAa,CAAC,aAAa;oBAAE;sBAAA0F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAEbjH,OAAA;kBAAKwG,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BzG,OAAA,CAACvB,MAAM;oBACL+H,SAAS,EAAC,WAAW;oBACrB8E,KAAK,EAAE;sBACLe,YAAY,EAAE,MAAM;sBACpBD,eAAe,EAAE,OAAO;sBACxB2B,KAAK,EAAE,SAAS;sBAChBP,MAAM,EAAE,MAAM;sBACdoB,UAAU,EAAE;oBACd,CAAE;oBACFvB,OAAO,EAAEpC,oBAAqB;oBAC9B6C,QAAQ,EAAE1L,qBAAqB,IAAIF,qBAAqB,IAAII,kCAAmC;oBAAAmE,QAAA,EAE9FnE,kCAAkC,GAAG,yBAAyB,GAC9DF,qBAAqB,GAAG,mBAAmB,GAAG;kBAAS;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eAETjH,OAAA,CAACZ,iBAAiB;oBAChByP,IAAI,EAAE3H,eAAgB;oBACtB4H,MAAM,EAAEA,CAAA,KAAM3H,kBAAkB,CAAC,KAAK,CAAE;oBACxC4H,SAAS,EAAE/D,YAAa;oBACxBgE,KAAK,EAAC,oBAAoB;oBAC1BlH,OAAO,EAAC,wDAAwD;oBAChEmH,iBAAiB,EAAC,QAAQ;oBAC1BhM,IAAI,EAAC;kBAAQ;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACZjH,OAAA;QAAAyG,QAAA,eACEzG,OAAA,CAACL,OAAO;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNjH,OAAA,CAACf,MAAM;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVjH,OAAA,CAACX,cAAc;MACbwP,IAAI,EAAEzH,kBAAmB;MACzB0H,MAAM,EAAEA,CAAA,KAAMzH,qBAAqB,CAAC,KAAK,CAAE;MAC3CgC,UAAU,EAAEjE,QAAS;MACrB8J,gBAAgB,EAAEtH,6BAA8B;MAChDuH,kBAAkB,EAAExN;IAAY;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFjH,OAAA,CAACF,gBAAgB;MACf+O,IAAI,EAAExO,sBAAuB;MAC7B+O,OAAO,EAAEA,CAAA,KAAM;QACb9O,yBAAyB,CAAC,KAAK,CAAC;MAClC;IAAE;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFjH,OAAA,CAACV,mBAAmB;MAClBuP,IAAI,EAAEvH,uBAAwB;MAC9B8H,OAAO,EAAEA,CAAA,KAAM;QACb7H,0BAA0B,CAAC,KAAK,CAAC;QACjCE,wBAAwB,CAAC,EAAE,CAAC;QAC5BE,uBAAuB,CAAC,EAAE,CAAC;MAC7B,CAAE;MACF0H,oBAAoB,EAAEA,CAAA,KAAM;QAC1B9H,0BAA0B,CAAC,KAAK,CAAC;QACjCE,wBAAwB,CAAC,EAAE,CAAC;QAC5BE,uBAAuB,CAAC,EAAE,CAAC;QAC3BN,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAE;MACFiI,YAAY,EAAE9H,qBAAsB;MACpCnG,aAAa,EAAEqG;IAAqB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/G,EAAA,CA15BID,gBAAgB;EAAA,QAGPV,cAAc,EACDA,cAAc,EAGbA,cAAc,EAGPA,cAAc,EAGnBA,cAAc,EAG1BJ,WAAW,EACXK,cAAc;AAAA;AAAA+P,EAAA,GAjB3BtP,gBAAgB;AA45BtB,eAAeA,gBAAgB;AAAC,IAAAsP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}